import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import { Metadata } from "next";
import { api } from "@/lib/api";
import MofseAdvance from "@/components/mofse-advance/MofseAdvance";
// import ProtectedRoute from "@/components/comman/ProtectedRoute";

export const metadata: Metadata = {
  title: "MOFSE - Learn",
  description: "Coming Soon",
};

const MofseQuant = async () => {
  const coins = await api.coins.getAll("marketCap", 0, 10, "DESC", "");
  const allCoins = coins.data.coins;
  const coinList = allCoins.filter((coin) =>
    ["USDT", "USDC"].includes(coin.symbol)
  );

  return (
    <div style={{ backgroundColor: '#222831', minHeight: '100vh' }}>
      <Header isDarkTheme={true} />
      <p className="text-center text-red-500">
        We&apos;re currently undergoing maintenance—please check back soon! Notes to be added.
      </p>
      <div className="center w-full">
        <MofseAdvance coinList={coinList} />
      </div>
      <Footer />
    </div>
  );
};

export default MofseQuant;
