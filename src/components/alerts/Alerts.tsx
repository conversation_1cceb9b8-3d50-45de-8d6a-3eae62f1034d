"use client";
import { Coin } from "@/lib/api.interface";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  TrendingUp,
  ChevronDown
} from "lucide-react";
// import {
//   Breadcrumb,
//   BreadcrumbItem,
//   BreadcrumbLink,
//   BreadcrumbList,
//   BreadcrumbSeparator,
// } from "@/components/ui/breadcrumb";
// import CreateAlertModal from "./CreateAlertModal";
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu";

// import { Triangle } from "lucide-react";
// import AlertCard from "./AlertCard";
// import { getIconAndSymbol } from "./utils";
// import { useAlertFilters } from "@/lib/state";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select";
// import { Pagination } from "../comman/Pagination";
// import Loader from "../comman/Loader";
// import { format } from "date-fns";
// import { MIN_ALERT_AMOUNT } from "@/lib/constant";
// import USDThresholdChart from "./USDThresholdChart";
import { FILTER_LIST } from "../mofse-advance/constant";
import { Wallet } from "lucide-react";
import Image from "next/image";
import AssetContent from "./AssetContent";
// import Link from "next/link";
// import Button from "../comman/Button";

// function getFromTo(selectedItem: string): { from: Date; to: Date } {
//   const to = new Date();
//   const from = new Date();

//   switch (selectedItem) {
//     case "24h":
//       from.setDate(from.getDate() - 1);
//       break;
//     case "7d":
//       from.setDate(from.getDate() - 7);
//       break;
//     case "1m":
//       from.setMonth(from.getMonth() - 1);
//       break;
//     case "6m":
//       from.setMonth(from.getMonth() - 6);
//       break;
//     default:
//       // Default to 24h, which matches the initial state
//       from.setDate(from.getDate() - 1);
//       break;
//   }

//   return { from, to };
// };

const Alerts = ({ coins }: { coins: Coin[] }) => {
  // const [currentPage, setCurrentPage] = useState(1);
  
  const [selectedCoin, setSelectedCoin] = useState<Coin | null>(coins[0]);
  const [filters] = useState([
  {
    title: "On-Chain Metrics",
    icon: <Wallet />,
    options: [
      {
        label: "Transaction Count",
        value: FILTER_LIST.TRANSACTION_COUNT,
      },
      {
        label: "Transaction Volume",
        value: FILTER_LIST.TRANSACTION_VOLUME,
      },
      {
        label: "Unique Sending Wallet",
        value: FILTER_LIST.UNIQUE_SENDING_WALLET,
      },
      {
        label: "Unique Receiving Wallet",
        value: FILTER_LIST.UNIQUE_RECEIVING_WALLET,
      },
      {
        label: "Total Unique Wallet",
        value: FILTER_LIST.TOTAL_UNIQUE_WALLET,
      },
      {
        label: "New Wallet Created",
        value: FILTER_LIST.NEW_WALLET_CREATED,
      },
      {
        label: "Block Mined",
        value: FILTER_LIST.BLOCK_MINED,
      },
      {
        label: "Average Transaction Value",
        value: FILTER_LIST.AVG_TRANSACTION_VALUE,
      },
      {
        label: "Total Fee",
        value: FILTER_LIST.TOTAL_FEE,
      },
      {
        label: "Wallet Sending > 1 " + selectedCoin?.symbol,
        value: FILTER_LIST.WALLET_SENDING_GTE_1,
      },
      {
        label: "Wallet Sending > 10 " + selectedCoin?.symbol,
        value: FILTER_LIST.WALLET_SENDING_GTE_10,
      },
      {
        label: "Wallet Sending > 100 " + selectedCoin?.symbol,
        value: FILTER_LIST.WALLET_SENDING_GTE_100,
      }
    ],
  },
  {
    title: "Coins Rankings & Market Cap",
    icon: <Wallet />,
    options: [
      {
        label: "Cryptocurrencies",
        value: FILTER_LIST.CRYPTOCURRENCIES,
      },
      {
        label: "Top Gainers",
        value: FILTER_LIST.TOP_GAINERS,
      },
      {
        label: "Top Losers",
        value: FILTER_LIST.TOP_LOSERS,
      },
    ]},
     {
    title: "Economic Indicators",
    icon: <Wallet />,
    options: [
      {
        label: "BTC vs Gold vs S&P 500",
        value: FILTER_LIST.BTCVSGOLDVSSP500,
      }
    ]}
]);
   const [activeFilter, setActiveFilter] = useState<string>(
      FILTER_LIST.TRANSACTION_COUNT
    );
  // const [selectedFilter, setSelectedFilter] = useState(
  //   MIN_ALERT_AMOUNT.toString()
  // );
  // const [assetFilter, setAssetFilter] = useState("usdt");
  // const [onChainFilter, setOnChainFilter] = useState(FILTER_LIST.TRANSACTION_COUNT);
  // const [alertFilter, setAlertFilter] = useState("spike-alerts");
  // const [open, setOpen] = useState(false);
  // const [selectedItem] = useState("24h");
  // const { from } = getFromTo(selectedItem);
  // const allTransfers = useAllTransfers();
  // const alertFiltersQuery = useAlertFilters();
  // const alertFiltersData = alertFiltersQuery.data;
  // const fromDate = new Date(from);
  // const formattedFromDate = format(fromDate, "dd-MM-yyyy");

  // Extract crypto type from selectedFilter if it's a crypto filter
  // const selectedCrypto = selectedFilter.startsWith("crypto:")
  //   ? selectedFilter.replace("crypto:", "")
  //   : "";

  // const bitcoinAlerts = useBlockchainHistory({
  //   page: currentPage,
  //   selectedFilter,
  //   from: formattedFromDate,
  //   selectedCrypto,
  // });
  // const alertHistory = bitcoinAlerts.data?.bitcoinAlerts || [];
  // const totalDocuments = bitcoinAlerts.data?.totalDocuments || 0;
  // const totalPages = Math.ceil(totalDocuments / 6);

  // Reset page when filter changes
  // useEffect(() => {
  //   setCurrentPage(1);
  // }, [selectedFilter]);

  // const isNegative = selectedCoin?.change.includes("-");
  // const updatedValue = isNegative
  //   ? selectedCoin?.change.replace("-", "")
  //   : selectedCoin?.change;
// "BTC", "ETH", "BNB", "SOL"
  const isBTC = selectedCoin?.symbol === "BTC";
  const isETH = selectedCoin?.symbol === "ETH";
  const isBNB = selectedCoin?.symbol === "BNB";
  const isSOL = selectedCoin?.symbol === "SOL";

  return (
    <div>
      <div className="py-0 text-white">
      <Tabs
        defaultValue={selectedCoin?.symbol}
        className="w-full overflow-x-auto"
        onValueChange={(val) => setSelectedCoin(coins.find((coin) => coin.symbol === val)!)}
      >
        <div className="border-b border-t border-gray-600 ">
          <TabsList className="flex space-x-4 overflow-x-auto whitespace-nowrap rounded-none px-2 h-13 p-0 bg-white" >
            {coins.map((coin) => (
              <TabsTrigger
                key={coin.symbol}
                value={coin.symbol}
                className="flex items-center space-x-1 rounded-none border-transparent px-3 py-6 text-sm font-medium border-0 text-gray-900 data-[state=active]:border-b-2 data-[state=active]:border-bg-primary data-[state=active]:text-[#bbd955] data-[state=active]:shadow-none hover:text-bg-primary transition-colors cursor-pointer"
                style={{ backgroundColor: 'transparent' }}
              >
                <Image
                  src={coin.iconUrl}
                  alt={coin.name}
                  width={24}
                  height={24}
                  className="mr-1"
                />
                <span className="text-base font-medium">{coin.symbol}</span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>
      </Tabs>
      <div className="flex tablet:flex-row flex-col">
        <div className="w-full tablet:w-96 tablet:h-screen tablet:overflow-y-auto tablet:border-r border-gray-600 py-4 pr-4 ">
          {filters.length > 0 && (
            <Accordion type="multiple" className="w-full"
            defaultValue={[filters[0].title]}
            > {/* Use "multiple" if you want more than one open at a time */}
              {filters.map((filter) => (
                <AccordionItem value={filter.title} key={filter.title} className="border-none">
                <AccordionTrigger className="group flex items-center justify-between w-full py-3 pl-2 pr-4 text-base font-medium text-gray-900 data-[state=open]:text-white hover:bg-gray-700 hover:text-gray-900 data-[state=open]:bg-gray-800 data-[state=closed]:bg-transparent hover:no-underline [&>svg]:w-5 [&>svg]:h-5 [&>svg]:shrink-0 [&>svg]:text-gray-400 [&[data-state=open]>svg]:text-white [&>svg]:transition-transform [&>svg]:duration-200 [&[data-state=open]>svg]:rotate-90">
   <span className="flex items-center justify-between gap-3 w-full cursor-pointer"> {/* Wrapper for icon and title */}
                      <div className="flex items-center gap-3">
                      {filter.icon}
                      {filter.title}
                      </div>
                      <ChevronDown/>
                    </span>
                  </AccordionTrigger>
                  <AccordionContent className="pb-1 pt-1">
                    <ul className="space-y-1 ml-2">
                      {filter.options.map((option) => (
                        <li
                          className={cn(
                            "flex items-center gap-2 cursor-pointer py-2 px-4 rounded",
                            "text-gray-900 hover:bg-bg-primary hover:text-black",
                            activeFilter === option.value
                              ? "bg-bg-primary text-black"
                              : ""
                          )}
                          onClick={() => setActiveFilter(option.value)}
                          key={option.value}
                        >
                          <TrendingUp className="mr-2" /> {option.label}
                        </li>
                      ))}
                    </ul>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          )}
        </div>

        <div className="w-full">
          
          {isBTC && (
            <AssetContent
              coin={coins.find((coin) => coin.symbol === "BTC")!}
              filter={activeFilter}
            />
          )}
          {isETH && (
            <AssetContent
              coin={coins.find((coin) => coin.symbol === "ETH")!}
              filter={activeFilter}
            />
          )}
          {isBNB && (
            <AssetContent
              coin={coins.find((coin) => coin.symbol === "BNB")!}
              filter={activeFilter}
            />
          )}
          {isSOL && (
            <AssetContent
              coin={coins.find((coin) => coin.symbol === "SOL")!}
              filter={activeFilter}
            />
          )}
        </div>
        </div>

    </div>
      {/* <p className="text-2xl text-center pt-4">Select On-Chain Parameters</p> */}
      {/* <div className="grid grid-cols-2 md:flex w-full justify-center pt-4 gap-2 md:gap-4">

      </div> */}
      {/* <Breadcrumb className="pb-2">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/alerts">On-Chain Insights</BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb> */}
      {/* <div className="grid grid-cols-1 tablet:grid-cols-2 lg:grid-cols-3  gap-8"> */}
      {/* <div> */}
        {/* <div className="order-2 tablet:order-1"> */}
          {/* <h1 className="text-3xl font-semibold">Custom On-Chain Alerts</h1> */}
          {/* <p className="text-sm mb-2">
            (All transactions are over $50 million USD)
          </p> */}
          {/* <div className="flex gap-2">
           */}
          {/* <div className="grid grid-cols-1 gap-x-4"> */}
            {/* <Select
              onValueChange={setSelectedFilter}
              defaultValue={selectedFilter}
            >
              <SelectTrigger className="w-full mb-4 mt-4">
                <SelectValue placeholder="Select a filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={MIN_ALERT_AMOUNT.toString()}>All</SelectItem>
                <SelectItem value="crypto:bitcoin">Bitcoin</SelectItem>
                <SelectItem value="crypto:ethereum">Ethereum</SelectItem>
                <SelectItem value="crypto:usdt">USDT</SelectItem>
                <SelectItem value="crypto:usdc">USDC</SelectItem>
                {alertFiltersData?.map((item) => (
                  <SelectItem key={item.alertName} value={item.alertName}>
                    {item.alertName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select> */}
            {/* Select Asset */}
            {/* <div>
              <label
                htmlFor="select-asset"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Select Asset
              </label>
              <Select onValueChange={setAssetFilter} defaultValue={assetFilter}>
                <SelectTrigger className="w-full mb-4">
                  <SelectValue placeholder="Select a filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="usdt">USDT</SelectItem>
                  <SelectItem value="usdc">USDC</SelectItem>
                  <SelectItem value="btc">BTC</SelectItem>
                  <SelectItem value="eth">ETH</SelectItem>
                </SelectContent>
              </Select>
            </div> */}

            {/* Select Alert */}
            {/* <div>
              <label
                htmlFor="select-alert"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Select Alert
              </label>
              <Select
                onValueChange={setAlertFilter}
                defaultValue={alertFilter}
                name="select-alert" // Add name for accessibility
              >
                <SelectTrigger className="w-full mb-4">
                  <SelectValue placeholder="Select a filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="spike-alerts">Spike Alerts</SelectItem>
                  <SelectItem value="drop-alerts">Drop Alerts</SelectItem>
                  <SelectItem value="anomaly-alerts">Anomaly Alerts</SelectItem>
                  <SelectItem value="threshold-alerts">
                    Threshold Alerts
                  </SelectItem>
                </SelectContent>
              </Select>
            </div> */}

            {/* Create Alert */}
            {/* <Link href={"/login"} className=" w-full">
              <Button className="w-full">Create Alert</Button>
            </Link> */}
          {/* </div> */}

          {/* <div className="flex h-[calc(100%-160px)]  justify-end items-end">
            <CreateAlertModal
              coins={coins}
              open={open}
              onOpenChange={setOpen}
            />
          </div> */}
          {/* </div> */}
          {/* <DropdownMenu>
          <DropdownMenuTrigger className="outline-none border border-bg-primary rounded p-2 flex items-center gap-2 cursor-pointer">
            <CalendarDays className="" />
            <p className="text-sm font-medium">
              {selectedItem}
            </p>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setSelectedItem("24h")}>
              24h
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSelectedItem("7d")}>
              7d
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSelectedItem("1m")}>
              1m
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setSelectedItem("6m")}>
              6m
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu> */}

          {/* <div className="mt-4 ">
          {bitcoinAlerts.isLoading ? <Loader /> : null}
          {alertHistory.length === 0 && !bitcoinAlerts.isLoading ? (
            <p>No alerts found</p>
          ) : null}
          {alertHistory.map((item, index) => {
            const { iconUrl, symbol } = getIconAndSymbol(item.assetType);

            return (
              <AlertCard
                key={index}
                item={item}
                iconUrl={iconUrl}
                symbol={symbol}
              />
            );
          })}
        </div>
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={setCurrentPage}
          className="my-4"
        /> */}
        {/* </div> */}
        {/* <div className="tablet:col-span-2 order-1 tablet:order-2"> */}
          {/* <div className="p-4 border border-border-light rounded"> */}
            {/* <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4"> */}
              {/* <div className="flex-1"> */}
                {/* <p className="text-text-primary text-sm font-semibold">
                  Quick Select
                </p> */}
                 {/* Select On-chain parameter  */}
            {/* <div className="md:ml-auto"> */}
              {/* <label
                htmlFor="select-onchain-parameter"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Select On-chain parameter
              </label> */}
              {/* <Select
                onValueChange={setOnChainFilter}
                defaultValue={onChainFilter}
                name="select-onchain-parameter" // Add name for accessibility
              >
                <SelectTrigger className="w-full md:w-[180px] md:mb-4">
                  <SelectValue placeholder="Select a filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="transaction-count">
                    Transaction Count
                  </SelectItem>
                  <SelectItem value="transaction-volume">
                    Transaction Volume
                  </SelectItem>
                  <SelectItem value="unique-sending-wallet">
                    Unique Sending Wallet
                  </SelectItem>
                  <SelectItem value="unique-receiving-wallet">
                    Unique Receiving Wallet
                  </SelectItem>
                  <SelectItem value="total-unique-wallet">
                    Total Unique Wallet
                  </SelectItem>
                  <SelectItem value="new-wallet-created">
                    New Wallet Created
                  </SelectItem>
                   <SelectItem value="block-mined">
                    Block Mined
                  </SelectItem>
                   <SelectItem value="avg-transaction-value">
                    Avg. Transaction Value
                  </SelectItem>
                  <SelectItem value="total-fee">
                    Total Fee
                  </SelectItem>
                  <SelectItem value="wallet-sending-gte-1">
                    Wallet Sending &gt; 1 {selectedCoin?.symbol}
                  </SelectItem>
                  <SelectItem value="wallet-sending-gte-10">
                     Wallet Sending &gt; 10 {selectedCoin?.symbol}
                  </SelectItem>
                  <SelectItem value="wallet-sending-gte-100">
                     Wallet Sending &gt; 100 {selectedCoin?.symbol}
                  </SelectItem>
                </SelectContent>
              </Select> */}
            {/* </div> */}
                {/* <div className="flex gap-2 mt-4 flex-wrap">
                  {coins.map((coin) => (
                    <div
                      className={cn(
                        "border border-border-light p-2 rounded w-fit text-text-primary text-xs cursor-pointer flex items-center gap-2",
                        selectedCoin?.uuid === coin.uuid && "bg-bg-primary "
                      )}
                      key={coin.uuid}
                      onClick={() => setSelectedCoin(coin)}
                    >
                      <picture>
                        <img
                          src={coin.iconUrl}
                          alt={coin.name}
                          height={20}
                          width={20}
                        />
                      </picture>
                      {coin.symbol} <span className="w-1" />
                    </div>
                  ))}
                </div> */}
              {/* </div> */}

            {/* </div> */}

            
          {/* </div> */}

          {/* {selectedCoin && ( */}
            {/* // <div className="mt-4"> */}
              {/* <div className="grid grid-cols-2 md:grid-cols-5 tablet:grid-cols-5 gap-2 my-8 text-text-primary">
                <div className="border border-border-light p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <span className="font-semibold text-base">
                      Latest Price
                    </span>
                    <picture>
                      <img
                        src={selectedCoin.iconUrl}
                        alt={selectedCoin.name}
                        height={20}
                        width={20}
                      />
                    </picture>
                  </div>
                  <p className="text-text-primary  mb-4 font-bold text-lg">
                    ${formatPrice(parseFloat(selectedCoin?.price))}
                  </p>
                </div>

                <div className="border border-border-light p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <span className="font-semibold text-base">Market Cap</span>
                    <picture>
                      <img
                        src={selectedCoin.iconUrl}
                        alt={selectedCoin.name}
                        height={20}
                        width={20}
                      />
                    </picture>
                  </div>
                  <p className="text-text-primary  mb-4 font-bold text-lg">
                    ${formatPriceInRoman(parseFloat(selectedCoin?.marketCap))}
                  </p>
                </div>

                <div className="border border-border-light p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <span className="font-semibold text-base">Volume</span>
                    <picture>
                      <img
                        src={selectedCoin.iconUrl}
                        alt={selectedCoin.name}
                        height={20}
                        width={20}
                      />
                    </picture>
                  </div>
                  <p className="text-text-primary  mb-4 font-bold text-lg">
                    $
                    {formatPriceInRoman(
                      parseFloat(selectedCoin?.["24hVolume"])
                    )}
                  </p>
                </div>

                <div className="border border-border-light p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <span className="font-semibold text-base">Dominance</span>
                    <picture>
                      <img
                        src={selectedCoin.iconUrl}
                        alt={selectedCoin.name}
                        height={20}
                        width={20}
                      />
                    </picture>
                  </div>
                  <p className="text-text-primary  mb-4 font-bold text-lg">
                    {selectedCoin?.dominance}%
                  </p>
                </div>

                <div className="border border-border-light p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <span className="font-semibold text-base">Change</span>
                    <picture>
                      <img
                        src={selectedCoin.iconUrl}
                        alt={selectedCoin.name}
                        height={20}
                        width={20}
                      />
                    </picture>
                  </div>
                  <span
                    className={`flex items-center text-xl gap-2 ${
                      isNegative ? "text-red-500" : "text-green-600"
                    }`}
                  >
                    <Triangle
                      className={`w-3 h-3 ${
                        isNegative ? "rotate-180" : "rotate-0"
                      }`}
                      fill={isNegative ? "red" : "green"}
                    />
                    {updatedValue}%
                  </span>
                </div>
              </div>
              <div className="flex gap-2 items-center mb-2">
                <picture>
                  <img
                    src={selectedCoin?.iconUrl}
                    alt={selectedCoin?.name}
                    height={32}
                    width={32}
                  />
                </picture>
                <p>{selectedCoin?.name}</p>
              </div> */}
              {/* <CoinChart coin={selectedCoin} /> */}
              {/* <TransactionCountChart assetType={selectedCoin.symbol} filter={'transaction-count'} />
               */}
              {/* <USDThresholdChart coin={selectedCoin} onChainFilter={onChainFilter}/> */}
            {/* </div> */}
          {/* )} */}
        {/* </div> */}
      {/* </div> */}
    </div>
  );
};

export default Alerts;
